package vn.vinclub.voucher.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.constant.MetadataKey;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherListingStatusChangeEvent;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingCreateDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingFilterDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingSpecification;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingUpdateDto;
import vn.vinclub.voucher.enums.VoucherListingStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.mapper.ProviderVoucherListingMapper;
import vn.vinclub.voucher.model.BaseEntity;
import vn.vinclub.voucher.model.ProviderVoucherListing;
import vn.vinclub.voucher.redis.RedisPublish;
import vn.vinclub.voucher.repository.ProviderVoucherListingRepository;
import vn.vinclub.voucher.service.EventService;
import vn.vinclub.voucher.service.ProviderMerchantService;
import vn.vinclub.voucher.service.ProviderService;
import vn.vinclub.voucher.service.ProviderVoucherListingService;
import vn.vinclub.voucher.util.CustomUtil;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProviderVoucherListingServiceImpl extends BaseService implements ProviderVoucherListingService {
    private final ProviderMerchantService providerMerchantService;
    private final ProviderService providerService;
    private final EventService eventService;

    private final ProviderVoucherListingRepository providerVoucherListingRepository;
    private final RedisPublish redisPublish;

    private final Cache<Long, ProviderVoucherListing> providerVoucherListingCacheById = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();

    @Override
    @Transactional
    public ProviderVoucherListing create(ProviderVoucherListingCreateDto createDto) {
        try (Profiler p = new Profiler(getClass(), "create")) {
            validateCreateRequest(createDto);
            var provider = providerService.getActiveByCode(createDto.getProviderCode());
            var providerVoucherListing = ProviderVoucherListingMapper.INSTANCE.toEntity(createDto);
            providerVoucherListing.setProviderId(provider.getId());
            providerVoucherListing.setBasePurchasePoint(amountToPoint(createDto.getPrice()));
            return this.providerVoucherListingRepository.save(providerVoucherListing);
        }
    }

    @Override
    @Transactional
    public ProviderVoucherListing update(Long id, ProviderVoucherListingUpdateDto updateDto) {
        try (Profiler p = new Profiler(getClass(), "update")) {
            var providerVoucherListing = this.providerVoucherListingRepository.findByIdAndActive(id, true)
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, ProviderVoucherListing.NAME, BaseEntity.Fields.id, id));
            var oldStatus = providerVoucherListing.getStatus();
            applyUpdateRequest(providerVoucherListing, updateDto);

            var savedProviderVoucherListing = this.providerVoucherListingRepository.save(providerVoucherListing);
            var newStatus = savedProviderVoucherListing.getStatus();

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PROVIDER_VOUCHER_LISTING, savedProviderVoucherListing.getId());
                }
            });

            // send event if status changed - no need using outbox
            if (!oldStatus.equals(newStatus)) {
                try {
                    eventService.sendEvent(ProviderVoucherListingStatusChangeEvent.builder()
                            .providerId(savedProviderVoucherListing.getProviderId())
                            .providerCode(savedProviderVoucherListing.getProviderCode())
                            .voucherListingId(savedProviderVoucherListing.getId())
                            .oldStatus(oldStatus)
                            .newStatus(newStatus)
                            .build());
                } catch (Exception e) {
                    log.error("Error sending event {}: {}", ProviderVoucherListingStatusChangeEvent.EVENT_CODE, e.getMessage(), e);
                }
            }
            return savedProviderVoucherListing;
        }
    }

    @Override
    @Transactional
    public boolean delete(Long id) {
        try (Profiler p = new Profiler(getClass(), "delete")) {
            var providerVoucherListing = this.providerVoucherListingRepository.findByIdAndActive(id, true)
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, ProviderVoucherListing.NAME, BaseEntity.Fields.id, id));
            var oldStatus = providerVoucherListing.getStatus();

            providerVoucherListing.setActive(false);
            providerVoucherListing.setStatus(VoucherListingStatusEnum.INACTIVE);
            var newStatus = providerVoucherListing.getStatus();
            var deletedProviderVoucherListing = this.providerVoucherListingRepository.save(providerVoucherListing);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PROVIDER_VOUCHER_LISTING, deletedProviderVoucherListing.getId());
                }
            });

            // send event if status changed - no need using outbox
            if (!oldStatus.equals(newStatus)) {
                try {
                    eventService.sendEvent(ProviderVoucherListingStatusChangeEvent.builder()
                            .providerId(deletedProviderVoucherListing.getProviderId())
                            .providerCode(deletedProviderVoucherListing.getProviderCode())
                            .voucherListingId(deletedProviderVoucherListing.getId())
                            .oldStatus(oldStatus)
                            .newStatus(newStatus)
                            .build());
                } catch (Exception e) {
                    log.error("Error sending event {}: {}", ProviderVoucherListingStatusChangeEvent.EVENT_CODE, e.getMessage(), e);
                }
            }
            return true;
        }
    }

    @Override
    public ProviderVoucherListing getById(Long id) {
        try (Profiler p = new Profiler(getClass(), "getById")) {
            var cachedProviderVoucherListing = providerVoucherListingCacheById.getIfPresent(id);
            if (cachedProviderVoucherListing == null) {
                try (Profiler cm = new Profiler(getClass(), "getById - cacheMiss")) {
                    var providerVoucherListingDb = providerVoucherListingRepository.findById(id)
                            .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, ProviderVoucherListing.NAME, BaseEntity.Fields.id, id));
                    putToCache(providerVoucherListingDb);
                    return providerVoucherListingDb;
                }
            }
            try (Profiler ch = new Profiler(getClass(), "getById - cacheHit")) {
                return cachedProviderVoucherListing;
            }
        }
    }

    @Override
    public Page<ProviderVoucherListing> filter(ProviderVoucherListingFilterDto filterDto, Pageable pageable) {
        try (Profiler p = new Profiler(getClass(), "filter")) {
            if (StringUtils.hasText(filterDto.getMerchantCode())) {
                var providerMerchantCode = providerMerchantService.lookupByVClubCode(filterDto.getProviderCode(), filterDto.getMerchantCode());
                if (StringUtils.hasText(providerMerchantCode)) {
                    filterDto.setProviderMerchantCode(providerMerchantCode);
                }
            }
            return providerVoucherListingRepository.findAll(ProviderVoucherListingSpecification.filterByCriteria(filterDto), pageable);
        }
    }

    @Override
    public void invalidateCache(Long id) {
        try (Profiler p = new Profiler(getClass(), "invalidateCache")) {
            providerVoucherListingCacheById.invalidate(id);
        }
    }

    private void putToCache(ProviderVoucherListing providerVoucherListing) {
        try (Profiler p = new Profiler(getClass(), "putToCache")) {
            providerVoucherListingCacheById.put(providerVoucherListing.getId(), providerVoucherListing);
        }
    }

    private void validateCreateRequest(ProviderVoucherListingCreateDto createDto) {
        if (!StringUtils.hasText(createDto.getProviderCode())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderVoucherListingCreateDto.Fields.providerCode);
        }
        if (!StringUtils.hasText(createDto.getProviderMerchantCode())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderVoucherListingCreateDto.Fields.providerMerchantCode);
        }

        if (!StringUtils.hasText(createDto.getProviderVoucherListingId())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderVoucherListingCreateDto.Fields.providerVoucherListingId);
        }

        if (!StringUtils.hasText(createDto.getVoucherName())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderVoucherListingCreateDto.Fields.voucherName);
        }

        if (Objects.isNull(createDto.getPrice())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderVoucherListingCreateDto.Fields.price);
        }

        if (Objects.isNull(createDto.getQuantity())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderVoucherListingCreateDto.Fields.quantity);
        }
    }

    private void applyUpdateRequest(ProviderVoucherListing providerVoucherListing, ProviderVoucherListingUpdateDto updateDto) {
        if (StringUtils.hasText(updateDto.getVoucherName())) {
            providerVoucherListing.setVoucherName(updateDto.getVoucherName());
        }

        if (Objects.nonNull(updateDto.getPrice())) {
            providerVoucherListing.setPrice(updateDto.getPrice());
            providerVoucherListing.setBasePurchasePoint(amountToPoint(updateDto.getPrice()));
        }

        if (Objects.nonNull(updateDto.getQuantity())) {
            providerVoucherListing.setQuantity(updateDto.getQuantity());
            if (updateDto.getQuantity() <= 0) {
                providerVoucherListing.setStatus(VoucherListingStatusEnum.OUT_OF_STOCK);
            }
        }

        if (Objects.nonNull(updateDto.getStatus())) {
            providerVoucherListing.setStatus(updateDto.getStatus());
        }

        if (Objects.nonNull(updateDto.getPromotionType())) {
            providerVoucherListing.setPromotionType(updateDto.getPromotionType());
        }

        if (Objects.nonNull(updateDto.getMetadata())) {
            providerVoucherListing.setMetadata(CustomUtil.mergeJsonNodes(providerVoucherListing.getMetadata(), updateDto.getMetadata(), MetadataKey.DISPLAY_DATA));
        }
    }
}
