package vn.vinclub.voucher.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.constant.MetadataKey;
import vn.vinclub.voucher.dto.EncryptKeySetting;
import vn.vinclub.voucher.dto.client.urbox.request.UrBoxBuyGiftReqDto;
import vn.vinclub.voucher.dto.client.urbox.request.UrBoxSearchBrandReqDto;
import vn.vinclub.voucher.dto.client.urbox.request.UrBoxSearchGiftReqDto;
import vn.vinclub.voucher.dto.client.urbox.response.UrBoxBuyGiftResponseDto;
import vn.vinclub.voucher.dto.client.urbox.response.UrBoxGetTxnDetailResponseDto;
import vn.vinclub.voucher.dto.client.urbox.response.UrBoxGiftDto;
import vn.vinclub.voucher.dto.client.urbox.response.UrBoxResponseDto;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherPurchaseRequestEvent;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingDetailsDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingDto;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedDto;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedStatusChangeReqDto;
import vn.vinclub.voucher.enums.EncryptKeyTypeEnum;
import vn.vinclub.voucher.enums.PurchasedVoucherStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.mapper.UrBoxMapper;
import vn.vinclub.voucher.model.VoucherPurchaseRequestHistory;
import vn.vinclub.voucher.model.VoucherPurchaseTransactionHistory;
import vn.vinclub.voucher.model.VoucherPurchased;
import vn.vinclub.voucher.service.ProviderIntegrationService;
import vn.vinclub.voucher.service.client.UrBoxProviderClient;

import java.math.BigDecimal;
import java.util.*;

@Service("urbox-integration-service")
@Slf4j
@RequiredArgsConstructor
public class UrBoxIntegrationServiceImpl extends ProviderIntegrationBaseService implements ProviderIntegrationService {
    private static final String PROVIDER_NAME = "Kho mã UrBox";
    private static final EncryptKeyTypeEnum ENCRYPTION_KEY_TYPE = EncryptKeyTypeEnum.RSA;
    private static final int ENCRYPTION_KEY_SIZE = 2048;

    private final UrBoxProviderClient urBoxProviderClient;

    @Getter
    @Value("${provider_integration.urbox.provider_code}")
    private String providerCode;

    @Value("${provider_integration.urbox.campaign.default}")
    private String campaignCode;

    @Value("${provider_integration.urbox.max-quantity-per-transaction}")
    private int maxQuantityPerTransaction;

    // region: Init provider
    @Override
    public void initProvider() {
        initProvider(providerCode, PROVIDER_NAME, EncryptKeySetting.builder()
                .keyType(ENCRYPTION_KEY_TYPE)
                .keySize(ENCRYPTION_KEY_SIZE)
                .build()
        );
    }

    @Override
    public boolean isActiveProvider() {
        try (Profiler p = new Profiler(getClass(), "isActiveProvider")) {
            return isActiveProvider(providerCode);
        }
    }

    @Override
    public boolean isConfiguredProvider() {
        try (Profiler p = new Profiler(getClass(), "isConfiguredProvider")) {
            return StringUtils.hasText(campaignCode) && StringUtils.hasText(providerCode) && maxQuantityPerTransaction > 0
                    && urBoxProviderClient.isConfigured();
        }
    }
    // endregion

    // region: search provider data
    @Override
    public ProviderVoucherListingDetailsDto getProviderVoucherListingDetail(String providerVoucherListingId, Long voucherListingId) {
        try (Profiler p = new Profiler(getClass(), "getProviderVoucherDetail")) {
            return getProviderVoucherListingDetail(providerCode, providerVoucherListingId, voucherListingId);
        }
    }

    @Override
    protected ProviderVoucherListingDetailsDto fetchProviderVoucherListingDetailFromApi(String providerVoucherListingId, Long voucherListingId) {
        try (Profiler p = new Profiler(getClass(), "fetchProviderVoucherListingDetailFromApi")) {
            var payload = UrBoxMapper.INSTANCE.toGetGiftDetailRequest(providerVoucherListingId);
            var response = urBoxProviderClient.getGiftDetail(payload, AppConst.Language.DEFAULT);

            if (Objects.nonNull(response) && response.isSuccess()) {
                var data = response.getData();
                if (Objects.nonNull(data)) {
                    // merge with English data
                    var enResponse = urBoxProviderClient.getGiftDetail(payload, AppConst.Language.EN);
                    data.mergeWithEnglishDto(enResponse.getData());

                    // Convert to DTO and return
                    return UrBoxMapper.INSTANCE.toProviderVoucherListingDetailDto(data);
                }
            }

            // If response is not successful or data is null, throw an exception
            var appErrorCode = Optional.ofNullable(response).map(UrBoxResponseDto::getVClubAppErrorCode).orElse(AppErrorCode.SYSTEM_ERROR);
            var errorMessage = Optional.ofNullable(response).map(UrBoxResponseDto::getMsg).orElse(AppErrorCode.SYSTEM_ERROR.getMessage());
            throw new BusinessLogicException(appErrorCode, errorMessage);
        }
    }
    // endregion

    // region: Purchase voucher
    @Override
    public void purchaseVoucher(ProviderVoucherPurchaseRequestEvent event) {
        try (Profiler p = new Profiler(getClass(), "purchaseVoucher")) {
            processPurchaseVoucher(event, maxQuantityPerTransaction);
        }
    }

    @Override
    public VoucherPurchasedDto rollbackVoucher(String voucherImportId, String voucherCode) {
        try (Profiler p = new Profiler(getClass(), "rollbackVoucher")) {
            throw new BusinessLogicException(AppErrorCode.ROLLBACK_VOUCHER_NOT_SUPPORTED);
        }
    }

    @Override
    public void retryTimeoutTransaction() {
        try (Profiler p = new Profiler(getClass(), "retryTimeoutTransaction")) {
            var timeoutTransactions = getProviderTimeoutTransactions(providerCode);
            if (CollectionUtils.isEmpty(timeoutTransactions)) {
                log.info("No timeout transactions to retry");
                return;
            }
            log.info("Found {} timeout transactions to retry", timeoutTransactions.size());
            rerunTimeoutTransactions(timeoutTransactions);
        }
    }

    @Override
    protected VoucherPurchaseTransactionHistory processProviderPurchaseTransaction(VoucherPurchaseRequestHistory voucherPurchaseRequest, int requestQuantity) {
        try (Profiler p = new Profiler(getClass(), "processProviderPurchaseTransaction")) {
            var txnId = voucherPurchaseTransactionHistoryService.genProviderTransactionId(voucherPurchaseRequest.getProviderCode());
            var siteUserId = voucherPurchaseRequest.getVoucherImportId();
            var voucherListing = providerVoucherListingService.getById(voucherPurchaseRequest.getVoucherListingId());
            if (!voucherListing.getProviderCode().equals(providerCode)) {
                log.error("voucherListingId {} not belong to provider {}", voucherListing.getId(), providerCode);
                throw new BusinessLogicException(AppErrorCode.REQUEST_DATA_INVALID, "voucherListingId not belong to provider");
            }

            var dataBuy = UrBoxMapper.INSTANCE.toDataBuy(voucherListing.getProviderVoucherListingId(), requestQuantity);
            var payload = UrBoxMapper.INSTANCE.toBuyGiftRequest(campaignCode, txnId, siteUserId, Collections.singletonList(dataBuy));

            // processing transaction
            var txn = voucherPurchaseTransactionHistoryService.initTransaction(voucherPurchaseRequest, requestQuantity, txnId, jsonUtils.readTreeFromObject(payload));

            return processPurchaseRequest(requestQuantity, payload, txn);
        }
    }

    @Override
    protected VoucherPurchaseTransactionHistory rerunTransaction(VoucherPurchaseTransactionHistory txn) {
        try (Profiler p = new Profiler(getClass(), "rerunProcessingTransaction")) {
            var requestQuantity = txn.getQuantity();
            var payload = jsonUtils.treeToValue(txn.getRequest(), UrBoxBuyGiftReqDto.class);

            return processPurchaseRequest(requestQuantity, payload, txn);
        }
    }

    private VoucherPurchaseTransactionHistory processPurchaseRequest(int requestQuantity, UrBoxBuyGiftReqDto payload, VoucherPurchaseTransactionHistory txn) {
        try (var p = new Profiler(getClass(), "processPurchaseRequest")) {
                var response = urBoxProviderClient.buyGift(payload, AppConst.Language.DEFAULT);
                txn.setResponse(jsonUtils.readTreeFromObject(response));
                txn.setProviderTransactionId(txn.getTransactionId());

                if (response.isSuccess()) {
                    var data = response.getData();
                    if (Objects.nonNull(data) && data.isCompleted()) {
                        txn = voucherPurchaseTransactionHistoryService.processTransactionSuccess(txn, requestQuantity, Long.valueOf(data.getCart().getMoneyTotal()), jsonUtils.readTreeFromObject(response));
                    } else {
                        throw new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_COMPLETED, providerCode);
                    }
                } else {
                    var errorMessage = Optional.ofNullable(response.getMsg()).filter(StringUtils::hasText).orElse(AppErrorCode.SYSTEM_ERROR.getMessage());
                    throw new BusinessLogicException(response.getVClubAppErrorCode(), errorMessage);
                }
            } catch (Exception e) {
                log.error("Error while processing purchase voucher: {}", e.getMessage());
                var errorMessage = Optional.of(e).map(Exception::getMessage).orElse(AppErrorCode.SYSTEM_ERROR.getMessage());
                var errorCode = AppErrorCode.SYSTEM_ERROR.getCode();
                if (e instanceof BusinessLogicException ble) {
                    errorMessage = ble.getPayload().getMessage().getFirst();
                    errorCode = ble.getPayload().getCode();
                }
                txn = voucherPurchaseTransactionHistoryService.processTransactionFailed(txn, requestQuantity, errorCode, errorMessage, txn.getResponse());
            }
        return txn;
    }

    @Override
    protected List<VoucherPurchased> extractPurchasedVouchers(VoucherPurchaseTransactionHistory txn) {
        try (Profiler p = new Profiler(getClass(), "extractPurchasedVouchers")) {
            var response = jsonUtils.convertValue(txn.getResponse(), new TypeReference<UrBoxResponseDto<UrBoxBuyGiftResponseDto>>() {
            });
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                var data = response.getData();
                if (data.isCompleted() && !CollectionUtils.isEmpty(data.getCart().getCodeLinkGift())) {
                    var gifts = data.getCart().getCodeLinkGift();
                    return gifts.stream().map(gift -> {
                                var providerVoucherPurchased = UrBoxMapper.INSTANCE.toVoucherPurchased(gift);
                                var orderData = JsonNodeFactory.instance.objectNode();
                                orderData.put(MetadataKey.OrderData.CAMPAIGN_CODE, campaignCode);
                                orderData.put(MetadataKey.OrderData.PROVIDER_VOUCHER_LISTING_ID, gift.getPriceId());
                                orderData.put(MetadataKey.OrderData.ORDER_ID, data.getCart().getId());
                                orderData.put(MetadataKey.OrderData.ORDER_DETAIL_ID, gift.getCartDetailId());

                                var metadata = JsonNodeFactory.instance.objectNode();
                                metadata.set(MetadataKey.ORDER_DATA, orderData);

                                providerVoucherPurchased.setMetadata(metadata);
                                return providerVoucherPurchased;
                            })
                            .peek(providerVoucherPurchased -> {
                                providerVoucherPurchased.setVoucherListingId(txn.getVoucherListingId());
                                providerVoucherPurchased.setVoucherImportId(txn.getVoucherImportId());
                                providerVoucherPurchased.setProviderId(txn.getProviderId());
                                providerVoucherPurchased.setProviderCode(txn.getProviderCode());
                                providerVoucherPurchased.setVoucherListingId(txn.getVoucherListingId());
                                providerVoucherPurchased.setTransactionId(txn.getTransactionId());
                                providerVoucherPurchased.setProviderTransactionId(txn.getProviderTransactionId());
                                providerVoucherPurchased.setStatus(PurchasedVoucherStatusEnum.ACTIVE);
                            }).toList();
                } else {
                    throw new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_COMPLETED, providerCode);
                }
            }
            var errorMessage = Optional.ofNullable(response.getMsg()).filter(StringUtils::hasText).orElse(AppErrorCode.SYSTEM_ERROR.getMessage());
            throw new BusinessLogicException(response.getVClubAppErrorCode(), errorMessage);
        }
    }
    // endregion

    // region: Sync data
    @Override
    public void syncProviderMerchant() {
        try (Profiler p = new Profiler(getClass(), "syncProviderMerchant")) {
            int batchSize = 500;
            int batchNo = 1;
            var latestMerchants = new ArrayList<ProviderMerchantDto>();

            try (Profiler p1 = new Profiler(getClass(), "pullUrBoxMerchant")) {
                log.info("|---------- Starting pull UrBox merchant data ----------|");
                do {
                    try (Profiler p2 = new Profiler(getClass(), "pullUrBoxMerchant - batch")) {
                        var req = UrBoxSearchBrandReqDto.builder()
                                .perPage(batchSize)
                                .pageNo(batchNo)
                                .build();
                        var response = urBoxProviderClient.searchBrands(req, AppConst.Language.VI);
                        if (response.isError()) {
                            log.error("|- Error while pulling UrBox merchant data: {}", response.getMsg());
                            throw new BusinessLogicException(response.getVClubAppErrorCode(), response.getMsg());
                        }
                        if (Objects.isNull(response.getData()) || CollectionUtils.isEmpty(response.getData().getItems())) {
                            break;
                        }
                        var responseEn = urBoxProviderClient.searchBrands(req, AppConst.Language.EN);
                        var items = response.getData().getItems();
                        var itemsEn = responseEn.getData().getItems();

                        for (int i = 0; i < items.size(); i++) {
                            var urBoxBrandId = items.get(i).getId();
                            var brandNameVi = items.get(i).getTitle();
                            var brandNameEn = Optional.ofNullable(itemsEn.get(i).getTitle()).orElse(brandNameVi);
                            latestMerchants.add(ProviderMerchantDto.builder()
                                    .providerCode(providerCode)
                                    .providerMerchantCode(urBoxBrandId)
                                    .displayNames(Map.of(AppConst.Language.VI, brandNameVi, AppConst.Language.EN, brandNameEn))
                                    .build());
                        }
                        batchNo++;
                    }
                } while (latestMerchants.size() == batchSize);
                log.info("|=> Total merchants pulled: {}", latestMerchants.size());
                log.info("|---------- Completed pull UrBox merchant data ----------|");
            }
            // process check and sync with latest data
            checkAndSyncProviderMerchant(providerCode, latestMerchants);
        }
    }

    @Override
    public void syncProviderVoucherListing() {
        try (Profiler p = new Profiler(getClass(), "syncProviderVoucherListing")) {
            int batchSize = 500;
            int batchNo = 1;
            var latestVoucherListings = new ArrayList<ProviderVoucherListingDto>();

            try (Profiler p1 = new Profiler(getClass(), "pullUrBoxVoucherListing")) {
                log.info("|---------- Starting pull UrBox voucher listing data ----------|");
                do {
                    try (Profiler p2 = new Profiler(getClass(), "pullUrBoxVoucherListing - batch")) {
                        var req = UrBoxSearchGiftReqDto.builder()
                                .perPage(batchSize)
                                .stock(1) // also include out of stock
                                .field("content,note,office") // include content, note, office in response
                                .pageNo(batchNo)
                                .build();
                        var response = urBoxProviderClient.searchGifts(req, AppConst.Language.VI);
                        if (response.isError()) {
                            log.error("|- Error while pulling UrBox voucher listing data: {}", response.getMsg());
                            throw new BusinessLogicException(response.getVClubAppErrorCode(), response.getMsg());
                        }
                        if (Objects.isNull(response.getData()) || CollectionUtils.isEmpty(response.getData().getItems())) {
                            break;
                        }
                        var items = response.getData().getItems();

                        for (UrBoxGiftDto item : items) {
                            var urBoxVoucherListingId = item.getId();
                            var voucherNameVi = item.getTitle();
                            var providerMerchantCode = item.getBrandId();
                            var price = BigDecimal.valueOf(Long.parseLong(item.getPrice()));
                            var quantity = Integer.parseInt(item.getQuantity());
                            var promotionType = item.getPromotionType();

                            var metadata = JsonNodeFactory.instance.objectNode();
                            metadata.put(MetadataKey.PROVIDER_CATEGORY_CODE, item.getCatId());

                            latestVoucherListings.add(ProviderVoucherListingDto.builder()
                                    .providerCode(providerCode)
                                    .providerMerchantCode(providerMerchantCode)
                                    .providerVoucherListingId(urBoxVoucherListingId)
                                    .voucherName(voucherNameVi)
                                    .price(price)
                                    .quantity(quantity)
                                    .promotionType(promotionType)
                                    .hasVoucherDetail(true)
                                    .metadata(metadata)
                                    .build());
                        }
                        batchNo++;
                    }
                } while (latestVoucherListings.size() == batchSize);
                log.info("|=> Total voucher listing pulled: {}", latestVoucherListings.size());
                log.info("|---------- Completed pull UrBox voucher listing  data ----------|");
            }
            // process check and sync with latest data
            checkAndSyncProviderVoucherListing(providerCode, latestVoucherListings);
        }
    }

    @Override
    public void syncPurchasedVoucherStatus() {
        try (Profiler p = new Profiler(getClass(), "syncPurchasedVoucherStatus")) {
            checkAndSyncPurchasedVoucherStatus(providerCode);
        }
    }

    @Override
    protected List<VoucherPurchasedStatusChangeReqDto> getPurchasedVouchersForChangeStatus(List<String> transactionIds) {
        try (Profiler p = new Profiler(getClass(), "getPurchasedVouchersForChangeStatus")) {
            var purchasedVouchersForChangeStatus = new ArrayList<VoucherPurchasedStatusChangeReqDto>();
            // call UrBox API to get transaction detail
            for (String transactionId : transactionIds) {
                try (var p1 = new Profiler(getClass(), "getTransactionDetail")) {
                    log.info("Get transaction detail from UrBox for transaction ID: {}", transactionId);
                    var payload = UrBoxMapper.INSTANCE.toGetTxnDetailRequest(transactionId);
                    var response = urBoxProviderClient.getTxnDetail(payload, AppConst.Language.DEFAULT);
                    if (Objects.nonNull(response) && response.isSuccess()) {
                        var data = response.getData();
                        if (Objects.nonNull(data) && Objects.nonNull(data.getDetail()) && !CollectionUtils.isEmpty(data.getDetail())) {
                            var details = data.getDetail();
                            for (UrBoxGetTxnDetailResponseDto.Detail detail : details) {
                                if (!PurchasedVoucherStatusEnum.ACTIVE.equals(detail.getVoucherStatus())) {
                                    purchasedVouchersForChangeStatus.add(VoucherPurchasedStatusChangeReqDto.builder()
                                            .providerCode(providerCode)
                                            .transactionId(transactionId)
                                            .status(detail.getVoucherStatus())
                                            .voucherRedeemedTime(detail.getVoucherRedeemedTime())
                                            .voucherCode(detail.getCode())
                                            .build());
                                }
                            }
                        } else {
                            log.warn("Transaction detail is empty for transaction ID: {} - status: {} - message: {}", transactionId, response.getStatus(), response.getMsg());
                        }
                    } else {
                        log.warn("Cannot get transaction detail from UrBox for transaction ID: {}", transactionId);
                    }
                } catch (Exception e) {
                    log.error("Error while getting transaction detail from UrBox for transaction ID: {}", transactionId, e);
                }
            }
            return purchasedVouchersForChangeStatus;
        }
    }
    // endregion

}
