package vn.vinclub.voucher.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.EncryptKeySetting;
import vn.vinclub.voucher.dto.VClubLock;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherPurchaseRequestEvent;
import vn.vinclub.voucher.dto.provider.ProviderCreateDto;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantCreateDto;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantDto;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantUpdateDto;
import vn.vinclub.voucher.dto.provider.voucher.*;
import vn.vinclub.voucher.enums.VoucherPurchaseRequestStatusEnum;
import vn.vinclub.voucher.enums.VoucherPurchaseTransactionStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.exception.IgnoreProcessingException;
import vn.vinclub.voucher.mapper.ProviderMerchantMapper;
import vn.vinclub.voucher.mapper.ProviderVoucherListingMapper;
import vn.vinclub.voucher.model.*;
import vn.vinclub.voucher.service.*;
import vn.vinclub.voucher.util.CustomUtil;
import vn.vinclub.voucher.util.KeyUtil;

import java.security.PublicKey;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class ProviderIntegrationBaseService extends BaseService {

    @Autowired
    protected VoucherPurchaseTransactionHistoryService voucherPurchaseTransactionHistoryService;

    @Autowired
    protected VoucherPurchaseRequestHistoryService voucherPurchaseRequestHistoryService;

    @Autowired
    protected ProviderVoucherListingService providerVoucherListingService;

    @Autowired
    protected VoucherPurchasedService voucherPurchasedService;

    @Autowired
    protected ProviderMerchantService providerMerchantService;

    @Autowired
    protected ProviderService providerService;

    @PersistenceContext
    private EntityManager entityManager;

    protected void initProvider(String providerCode, String providerName, EncryptKeySetting encryptKeySetting) {
        log.info("|------------------------------ START INIT PROVIDER ------------------------------|");
        try (Profiler p = new Profiler(getClass(), "initProvider")) {
            if (!providerService.existsByCode(providerCode)) {
                var createdProvider = providerService.create(
                        ProviderCreateDto.builder()
                                .code(providerCode)
                                .name(providerName)
                                .encryptKeySetting(encryptKeySetting)
                                .build()
                );
                log.info("|-- Initial provider {} successfully", providerCode);
                PublicKey publicKey = KeyUtil.decodePublicKey(createdProvider.getEncryptionKey().getPublicKey());
                log.info("|-- Please share the public key as below to {}: \n{}", providerCode, KeyUtil.printFormatKey(publicKey));
            } else {
                log.info("|-- Provider {} already initialized!", providerCode);
            }
        } catch (Exception e) {
            log.error("|-- Error initializing provider {}: {}", providerCode, e.getMessage());
        }
        log.info("|------------------------------ END INIT PROVIDER ------------------------------|");
    }

    protected boolean isActiveProvider(String providerCode) {
        try {
            providerService.getActiveByCode(providerCode);
            return true;
        } catch (BusinessLogicException e) {
            return false;
        } catch (Exception e) {
            log.error("Error while checking if provider {} is active: {}", providerCode, e.getMessage());
            throw e;
        }
    }

    protected void processPurchaseVoucher(ProviderVoucherPurchaseRequestEvent event, Integer maxQuantityPerRequest) {
        try (Profiler p = new Profiler(getClass(), "processPurchaseVoucher")) {
            var voucherPurchaseRequest = voucherPurchaseRequestHistoryService.findByVoucherImportId(event.getVoucherImportId());
            if (Objects.isNull(voucherPurchaseRequest)) {
                log.error("Voucher purchase request not found for import id: {}. Skipped!!!", event.getVoucherImportId());
                throw new IgnoreProcessingException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
            }
            if (VoucherPurchaseRequestStatusEnum.COMPLETED.equals(voucherPurchaseRequest.getStatus())) {
                log.info("Voucher purchase request {} has been completed. Skipped!!!", event.getVoucherImportId());
                throw new IgnoreProcessingException(AppErrorCode.EVENT_PROCESSED, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
            }

            try (var lock = new VClubLock(redissonClient, AppConst.RedisLock.VOUCHER_PURCHASE_REQUEST.withPostFix(voucherPurchaseRequest.getVoucherImportId()))) {
                // detach the voucher purchase request from the entity manager for get fresh data
                entityManager.detach(voucherPurchaseRequest);

                // Get the latest requestHistory of the voucher purchase request
                var requestHistory = voucherPurchaseRequestHistoryService.findByVoucherImportId(voucherPurchaseRequest.getVoucherImportId());
                if (Objects.isNull(requestHistory)) {
                    log.error("Voucher purchase request requestHistory not found for import id: {}. Skipped!!!", event.getVoucherImportId());
                    throw new IgnoreProcessingException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
                }
                if (VoucherPurchaseRequestStatusEnum.COMPLETED.equals(requestHistory.getStatus())) {
                    log.info("Voucher purchase request {} has been completed. Skipped!!!", event.getVoucherImportId());
                    throw new IgnoreProcessingException(AppErrorCode.EVENT_PROCESSED, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
                }

                requestHistory = recheckLastProcessing(requestHistory);

                var isCompleted = false;
                var remainingQuantity = requestHistory.getRequestQuantity() - requestHistory.getPurchasedQuantity();
                var errorMessage = AppErrorCode.SYSTEM_ERROR.getMessage();
                var errorCode = AppErrorCode.SYSTEM_ERROR.getCode();

                do {
                    var requestQuantity = Math.min(remainingQuantity, maxQuantityPerRequest);

                    var txn = processProviderPurchaseTransaction(requestHistory, requestQuantity);

                    if (VoucherPurchaseTransactionStatusEnum.FAILED.equals(txn.getStatus())) {
                        log.error("Transaction {} failed with error: {}", txn.getTransactionId(), txn.getErrorMessage());
                        errorCode = txn.getErrorCode();
                        errorMessage = txn.getErrorMessage();
                        isCompleted = true;
                    } else {
                        var purchasedVouchers = extractPurchasedVouchers(txn);
                        requestHistory = voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
                        remainingQuantity -= txn.getQuantity();
                        if (remainingQuantity <= 0) {
                            isCompleted = true;
                        }
                    }
                } while (!isCompleted);

                voucherPurchaseRequestHistoryService.completePurchaseRequest(requestHistory, errorCode, errorMessage);
            }
        }
    }

    private VoucherPurchaseRequestHistory recheckLastProcessing(VoucherPurchaseRequestHistory requestHistory) {
        try (Profiler p = new Profiler(getClass(), "recheckProcessingRequestHistory")) {
            var lastTransactionList = voucherPurchaseTransactionHistoryService.getByVoucherImportId(requestHistory.getVoucherImportId());

            if (CollectionUtils.isEmpty(lastTransactionList)) {
                return requestHistory;
            }

            var latestTransaction = lastTransactionList.stream()
                    .max(Comparator.comparingLong(VoucherPurchaseTransactionHistory::getCreatedTime)).get();

            if (VoucherPurchaseTransactionStatusEnum.FAILED.equals(latestTransaction.getStatus())) {
                return requestHistory;
            }

            if (VoucherPurchaseTransactionStatusEnum.PROCESSING.equals(latestTransaction.getStatus())) {
                var rerunTransaction = rerunTransaction(latestTransaction);
                if (VoucherPurchaseTransactionStatusEnum.SUCCESS.equals(rerunTransaction.getStatus())) {
                    var purchasedVouchers = extractPurchasedVouchers(rerunTransaction);
                    return voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
                }
                return requestHistory;
            }

            if (VoucherPurchaseTransactionStatusEnum.SUCCESS.equals(latestTransaction.getStatus())) {
                var successQuantity = lastTransactionList.stream()
                        .filter(txn -> VoucherPurchaseTransactionStatusEnum.SUCCESS.equals(txn.getStatus()))
                        .mapToInt(VoucherPurchaseTransactionHistory::getQuantity)
                        .sum();

                // check if the success quantity is equal to the purchased quantity -> last transaction is completed update to request history
                if (Objects.equals(successQuantity, requestHistory.getPurchasedQuantity())) {
                    return requestHistory;
                }

                var purchasedVouchers = extractPurchasedVouchers(latestTransaction);
                return voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
            }

            throw new BusinessLogicException(AppErrorCode.SYSTEM_ERROR);
        }
    }

    protected List<VoucherPurchaseTransactionHistory> getProviderTimeoutTransactions(String providerCode) {
        try (Profiler p = new Profiler(getClass(), "getProviderTimeoutTransactions")) {
            return voucherPurchaseTransactionHistoryService.findByErrorCode(providerCode, AppErrorCode.EXTERNAL_API_CALL_TIMEOUT.getCode());
        }
    }

    protected void rerunTimeoutTransactions(List<VoucherPurchaseTransactionHistory> timeoutTransactions) {
        try (Profiler p = new Profiler(getClass(), "rerunTimeoutTransactions")) {
            // group by voucherImportId
            var groupedTransactions = new HashMap<String, List<VoucherPurchaseTransactionHistory>>();
            for (var txn : timeoutTransactions) {
                var voucherImportId = txn.getVoucherImportId();
                if (!groupedTransactions.containsKey(voucherImportId)) {
                    groupedTransactions.put(voucherImportId, new ArrayList<>());
                }
                groupedTransactions.get(voucherImportId).add(txn);
            }

            // process each group
            for (var entry : groupedTransactions.entrySet()) {
                var voucherImportId = entry.getKey();
                var transactions = entry.getValue();
                log.info("Retrying timeout transactions for voucherImportId: {}", voucherImportId);
                try (var lock = new VClubLock(redissonClient, AppConst.RedisLock.VOUCHER_PURCHASE_REQUEST.withPostFix(voucherImportId))) {
                    // Get the latest requestHistory of the voucher purchase request
                    var requestHistory = voucherPurchaseRequestHistoryService.findByVoucherImportId(voucherImportId);
                    if (Objects.isNull(requestHistory)) {
                        log.error("Voucher purchase request not found for voucherImportId: {}", voucherImportId);
                        continue;
                    }
                    if (VoucherPurchaseRequestStatusEnum.PROCESSING.equals(requestHistory.getStatus())) {
                        log.warn("Voucher purchase request is still processing, skipped retrying timeout transactions for voucherImportId: {}", voucherImportId);
                        continue;
                    }

                    var totalPurchasedQuantity = requestHistory.getPurchasedQuantity();

                    for (var txn : transactions) {
                        try {
                            var rerunTxn = rerunTransaction(txn);
                            if (VoucherPurchaseTransactionStatusEnum.FAILED.equals(rerunTxn.getStatus())) {
                                log.error("Rerun timeout transaction {} failed with error: {}", rerunTxn.getTransactionId(), rerunTxn.getErrorMessage());
                            } else {
                                var purchasedVouchers = extractPurchasedVouchers(rerunTxn);
                                requestHistory = voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
                                totalPurchasedQuantity += purchasedVouchers.size();
                            }
                        } catch (Exception e) {
                            log.error("Error while retrying timeout transaction {}", txn.getTransactionId(), e);
                        }
                    }
                    if (totalPurchasedQuantity > 0) {
                        voucherPurchaseRequestHistoryService.completePurchaseRequest(requestHistory, null, null);
                    }
                }
            }
        }
    }

    protected void checkAndSyncProviderMerchant(String providerCode, List<ProviderMerchantDto> latestProviderMerchants) {
        log.info("|---------- START CHECK AND SYNC {} MERCHANT ----------|", providerCode);
        try (Profiler p = new Profiler(getClass(), "checkAndSyncProviderMerchant")) {
            // Process in batches of 500
            int pageSize = 500;
            int pageNumber = 0;
            List<ProviderMerchant> currentProviderMerchants = new ArrayList<>();

            while (true) {
                var page = providerMerchantService.getAllByProviderCode(
                    providerCode, 
                    Pageable.ofSize(pageSize).withPage(pageNumber)
                );
                var batchMerchants = page.getContent();
                if (batchMerchants.isEmpty()) {
                    break;
                }
                currentProviderMerchants.addAll(batchMerchants);
                if (!page.hasNext()) {
                    break;
                }
                pageNumber++;
            }

            if (CollectionUtils.isEmpty(currentProviderMerchants) && CollectionUtils.isEmpty(latestProviderMerchants)) {
                return;
            }

            List<ProviderMerchantCreateDto> createList = new ArrayList<>();
            Map<Long, ProviderMerchantUpdateDto> updateList = new HashMap<>();
            List<Long> deleteList = new ArrayList<>();

            if (CollectionUtils.isEmpty(currentProviderMerchants)) {
                createList = latestProviderMerchants.stream()
                        .map(ProviderMerchantMapper.INSTANCE::toCreateDto)
                        .peek(createDto -> createDto.setProviderCode(providerCode))
                        .toList();
                doCreateNewProviderMerchants(createList);
                return;
            }

            if (CollectionUtils.isEmpty(latestProviderMerchants)) {
                deleteList = currentProviderMerchants.stream()
                        .map(ProviderMerchant::getId)
                        .toList();

                doDeleteProviderMerchants(deleteList);
                return;
            }

            // compare current and latest provider merchants
            Map<String, ProviderMerchant> currentMap = currentProviderMerchants.stream()
                    .collect(Collectors.toMap(ProviderMerchant::getProviderMerchantCode, Function.identity()));
            Map<String, ProviderMerchantDto> latestMap = latestProviderMerchants.stream()
                    .collect(Collectors.toMap(ProviderMerchantDto::getProviderMerchantCode, Function.identity()));

            // find out which merchants need to be deleted, updated
            for (var current : currentMap.entrySet()) {
                var currentMerchant = current.getValue();
                var latestMerchant = latestMap.get(current.getKey());
                if (Objects.isNull(latestMerchant)) {
                    deleteList.add(currentMerchant.getId());
                } else {
                    // check if need to update
                    if (!currentMerchant.getDisplayNames().equals(latestMerchant.getDisplayNames())) {
                        var updateDto = ProviderMerchantMapper.INSTANCE.toUpdateDto(latestMerchant);
                        updateList.put(currentMerchant.getId(), updateDto);
                    }
                }
            }

            // find out which merchants need to be created
            for (var latest : latestMap.entrySet()) {
                var currentMerchant = currentMap.get(latest.getKey());
                if (Objects.isNull(currentMerchant)) {
                    var createDto = ProviderMerchantMapper.INSTANCE.toCreateDto(latest.getValue());
                    createDto.setProviderCode(providerCode);
                    createList.add(createDto);
                }
            }
            // process delete, update, create
            if (!CollectionUtils.isEmpty(deleteList)) {
                doDeleteProviderMerchants(deleteList);
            }

            if (!MapUtils.isEmpty(updateList)) {
                doUpdateProviderMerchants(updateList);
            }

            if (!CollectionUtils.isEmpty(createList)) {
                doCreateNewProviderMerchants(createList);
            }
        }
        log.info("|---------- END CHECK AND SYNC {} MERCHANT ----------|", providerCode);
    }

    private void doUpdateProviderMerchants(Map<Long, ProviderMerchantUpdateDto> updateList) {
        if (MapUtils.isEmpty(updateList)) {
            return;
        }
        log.info("|- Total {} provider merchants need to be updated", updateList.size());
        int updated = 0;
        int failed = 0;

        try (Profiler p = new Profiler(getClass(), "doUpdateProviderMerchants")) {
            for (var entry : updateList.entrySet()) {
                try {
                    providerMerchantService.update(entry.getKey(), entry.getValue());
                    updated++;
                } catch (Exception e) {
                    log.error("|-- Error while updating provider merchant with id {}: {}", entry.getKey(), e.getMessage());
                    failed++;
                }
            }
        }
        log.info("|=> Update provider merchant success {} - failed {}", updated, failed);
    }

    private void doDeleteProviderMerchants(List<Long> deleteList) {
        if (CollectionUtils.isEmpty(deleteList)) {
            return;
        }
        log.info("|- Total {} provider merchants need to be deleted", deleteList.size());
        int deleted = 0;
        int failed = 0;

        try (Profiler p = new Profiler(getClass(), "doDeleteProviderMerchants")) {
            for (var id : deleteList) {
                try {
                    providerMerchantService.delete(id);
                    deleted++;
                } catch (Exception e) {
                    log.error("|-- Error while deleting provider merchant with id {}: {}", id, e.getMessage());
                    failed++;
                }
            }
        }
        log.info("|=> Delete provider merchant success {} - failed {}", deleted, failed);
    }

    private void doCreateNewProviderMerchants(List<ProviderMerchantCreateDto> createList) {
        if (CollectionUtils.isEmpty(createList)) {
            return;
        }
        log.info("|- Total {} provider merchants need to be created", createList.size());
        int created = 0;
        int failed = 0;

        try (Profiler p = new Profiler(getClass(), "doCreateNewProviderMerchants")) {
            for (var createDto : createList) {
                try {
                    providerMerchantService.create(createDto);
                    created++;
                } catch (Exception e) {
                    log.error("|-- Error while creating provider merchant with code {}: {}", createDto.getProviderMerchantCode(), e.getMessage());
                    failed++;
                }
            }
        }
        log.info("|=> Create provider merchant success {} - failed {}", created, failed);
    }

    protected void checkAndSyncProviderVoucherListing(String providerCode, List<ProviderVoucherListingDto> latestVoucherListings) {
        log.info("|---------- START CHECK AND SYNC {} VOUCHER LISTING ----------|", providerCode);
        try (Profiler p = new Profiler(getClass(), "checkAndSyncProviderVoucherListing")) {
            // Process in batches of 500
            int pageSize = 500;
            int pageNumber = 0;
            List<ProviderVoucherListing> currentVoucherListings = new ArrayList<>();

            while (true) {
                var page = providerVoucherListingService.filter(
                    ProviderVoucherListingFilterDto.builder().providerCode(providerCode).status(null).build(),
                    Pageable.ofSize(pageSize).withPage(pageNumber)
                );
                var batchListings = page.getContent();
                if (batchListings.isEmpty()) {
                    break;
                }
                currentVoucherListings.addAll(batchListings);
                if (!page.hasNext()) {
                    break;
                }
                pageNumber++;
            }

            if (CollectionUtils.isEmpty(currentVoucherListings) && CollectionUtils.isEmpty(latestVoucherListings)) {
                return;
            }

            List<ProviderVoucherListingCreateDto> createList = new ArrayList<>();
            Map<Long, ProviderVoucherListingUpdateDto> updateList = new HashMap<>();
            List<Long> deleteList = new ArrayList<>();

            if (CollectionUtils.isEmpty(currentVoucherListings)) {
                createList = latestVoucherListings.stream()
                        .map(ProviderVoucherListingMapper.INSTANCE::toCreateDto)
                        .peek(createDto -> createDto.setProviderCode(providerCode))
                        .toList();
                doCreateNewProviderVoucherListings(createList);
                return;
            }

            if (CollectionUtils.isEmpty(latestVoucherListings)) {
                deleteList = currentVoucherListings.stream()
                        .map(ProviderVoucherListing::getId)
                        .toList();
                doDeleteProviderVoucherListings(deleteList);
                return;
            }

            // compare current and latest voucher listings
            Map<String, ProviderVoucherListing> currentMap = currentVoucherListings.stream()
                    .collect(Collectors.toMap(ProviderVoucherListing::getProviderVoucherListingId, Function.identity()));
            Map<String, ProviderVoucherListingDto> latestMap = latestVoucherListings.stream()
                    .collect(Collectors.toMap(ProviderVoucherListingDto::getProviderVoucherListingId, Function.identity()));

            // find out which voucher listings need to be deleted, updated
            for (var current : currentMap.entrySet()) {
                var currentListing = current.getValue();
                var latestListing = latestMap.get(current.getKey());
                if (Objects.isNull(latestListing)) {
                    deleteList.add(currentListing.getId());
                } else {
                    // check if need to update
                    if (checkIfVoucherListingNeedUpdate(currentListing, latestListing)) {
                        var updateDto = ProviderVoucherListingMapper.INSTANCE.toUpdateDto(latestListing);
                        updateList.put(currentListing.getId(), updateDto);
                    }
                }
            }

            // find out which voucher listings need to be created
            for (var latest : latestMap.entrySet()) {
                var currentListing = currentMap.get(latest.getKey());
                if (Objects.isNull(currentListing)) {
                    var createDto = ProviderVoucherListingMapper.INSTANCE.toCreateDto(latest.getValue());
                    createList.add(createDto);
                }
            }

            // process delete, update, create
            if (!CollectionUtils.isEmpty(deleteList)) {
                doDeleteProviderVoucherListings(deleteList);
            }

            if (!MapUtils.isEmpty(updateList)) {
                doUpdateProviderVoucherListings(updateList);
            }

            if (!CollectionUtils.isEmpty(createList)) {
                doCreateNewProviderVoucherListings(createList);
            }
        }
        log.info("|---------- END CHECK AND SYNC {} VOUCHER LISTING ----------|", providerCode);
    }

    private boolean checkIfVoucherListingNeedUpdate(ProviderVoucherListing currentListing, ProviderVoucherListingDto latestListing) {
        var isDiffQuantity = !Objects.equals(currentListing.getQuantity(), latestListing.getQuantity());
        var isDiffPrice = !Objects.equals(currentListing.getPrice().longValue(), latestListing.getPrice().longValue());
        var isDiffVoucherName = !Objects.equals(currentListing.getVoucherName(), latestListing.getVoucherName());
        var isDiffPromotionType = !Objects.equals(currentListing.getPromotionType(), latestListing.getPromotionType());
        var isDiffMetadata = !CustomUtil.compareJsonNodes(currentListing.getMetadata(), latestListing.getMetadata());
        return isDiffQuantity || isDiffPrice || isDiffVoucherName || isDiffPromotionType || isDiffMetadata;
    }

    private void doUpdateProviderVoucherListings(Map<Long, ProviderVoucherListingUpdateDto> updateList) {
        if (MapUtils.isEmpty(updateList)) {
            return;
        }
        log.info("|- Total {} provider voucher listings need to be updated", updateList.size());
        int updated = 0;
        int failed = 0;

        try (Profiler p = new Profiler(getClass(), "doUpdateProviderVoucherListings")) {
            for (var entry : updateList.entrySet()) {
                try {
                    providerVoucherListingService.update(entry.getKey(), entry.getValue());
                    updated++;
                } catch (Exception e) {
                    log.error("|-- Error while updating provider voucher listing with id {}: {}", entry.getKey(), e.getMessage());
                    failed++;
                }
            }
        }
        log.info("|=> Update provider voucher listing success {} - failed {}", updated, failed);
    }

    private void doDeleteProviderVoucherListings(List<Long> deleteList) {
        if (CollectionUtils.isEmpty(deleteList)) {
            return;
        }
        log.info("|- Total {} provider voucher listings need to be deleted", deleteList.size());
        int deleted = 0;
        int failed = 0;

        try (Profiler p = new Profiler(getClass(), "doDeleteProviderVoucherListings")) {
            for (var id : deleteList) {
                try {
                    providerVoucherListingService.delete(id);
                    deleted++;
                } catch (Exception e) {
                    log.error("|-- Error while deleting provider voucher listing with id {}: {}", id, e.getMessage());
                    failed++;
                }
            }
        }
        log.info("|=> Delete provider voucher listing success {} - failed {}", deleted, failed);
    }

    private void doCreateNewProviderVoucherListings(List<ProviderVoucherListingCreateDto> createList) {
        if (CollectionUtils.isEmpty(createList)) {
            return;
        }
        log.info("|- Total {} provider voucher listings need to be created", createList.size());
        int created = 0;
        int failed = 0;

        try (Profiler p = new Profiler(getClass(), "doCreateNewProviderVoucherListings")) {
            for (var createDto : createList) {
                try {
                    providerVoucherListingService.create(createDto);
                    created++;
                } catch (Exception e) {
                    log.error("|-- Error while creating provider voucher listing with id {}: {}", createDto.getProviderVoucherListingId(), e.getMessage());
                    failed++;
                }
            }
        }
        log.info("|=> Create provider voucher listing success {} - failed {}", created, failed);
    }

    protected void checkAndSyncPurchasedVoucherStatus(String providerCode) {
        log.info("|---------- START CHECK AND SYNC PURCHASED {} VOUCHER STATUS ----------|", providerCode);
        try (var p = new Profiler(getClass(), "checkAndSyncPurchasedVoucherStatus")) {
            // Get all transaction IDs with active purchased vouchers
            var transactionIds = voucherPurchasedService.getAllTransactionIdsWithActivePurchasedVouchers(providerCode);
            if (CollectionUtils.isEmpty(transactionIds)) {
                log.info("|- No transaction IDs with active purchased vouchers to sync");
                return;
            }

            var purchasedVouchersForChangeStatus = getPurchasedVouchersForChangeStatus(transactionIds);

            if (CollectionUtils.isEmpty(purchasedVouchersForChangeStatus)) {
                log.info("|- No purchased voucher status change required");
                return;
            }
            int updated = 0;
            int failed = 0;

            // save updated vouchers and send outbox event
            for (var updatedVoucher : purchasedVouchersForChangeStatus) {
                try (var l = new VClubLock(redissonClient, AppConst.RedisLock.VOUCHER_PURCHASED.withPostFix(updatedVoucher.getTransactionId()) + "_" + updatedVoucher.getVoucherCode())) {
                    voucherPurchasedService.changeVoucherStatus(updatedVoucher);
                    updated++;
                } catch (Exception e) {
                    log.error("|-- Error while changing status of purchased voucher {}: {}", updatedVoucher.getVoucherCode(), e.getMessage(), e);
                    failed++;
                }
            }
            log.info("|=> Updated {} purchased voucher status successfully - failed {}", updated, failed);
        }
        log.info("|---------- END CHECK AND SYNC PURCHASED {} VOUCHER STATUS ----------|", providerCode);
    }

    // Abstract methods to be implemented by subclasses
    protected abstract VoucherPurchaseTransactionHistory processProviderPurchaseTransaction(VoucherPurchaseRequestHistory voucherPurchaseRequest, int requestQuantity);

    protected abstract VoucherPurchaseTransactionHistory rerunTransaction(VoucherPurchaseTransactionHistory txn);

    protected abstract List<VoucherPurchased> extractPurchasedVouchers(VoucherPurchaseTransactionHistory txn);

    protected abstract List<VoucherPurchasedStatusChangeReqDto> getPurchasedVouchersForChangeStatus(List<String> transactionIds);
}
