package vn.vinclub.voucher.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.constant.MetadataKey;
import vn.vinclub.voucher.dto.provider.ProviderDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingDetailsDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherPickupReqDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherPickupResponseDto;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedDto;
import vn.vinclub.voucher.enums.VoucherPurchaseRequestStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.mapper.ProviderMapper;
import vn.vinclub.voucher.mapper.ProviderVoucherListingMapper;
import vn.vinclub.voucher.mapper.VoucherPurchaseMapper;
import vn.vinclub.voucher.model.ProviderVoucherListing;
import vn.vinclub.voucher.model.VoucherPurchaseRequestHistory;
import vn.vinclub.voucher.service.*;
import vn.vinclub.voucher.service.factory.ProviderIntegrationServiceFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Service
@Slf4j
@RequiredArgsConstructor
public class InternalVoucherServiceImpl extends BaseService implements InternalVoucherService {
    private final VoucherPurchaseRequestHistoryService voucherPurchaseRequestHistoryService;
    private final ProviderIntegrationServiceFactory providerIntegrationServiceFactory;
    private final VoucherPurchasedService voucherPurchasedService;
    private final ProviderService providerService;
    private final ProviderVoucherListingService providerVoucherListingService;

    @Override
    public ProviderDto getProviderById(Long providerId) {
        try (var p = new Profiler(getClass(), "getProviderById")) {
            return ProviderMapper.INSTANCE.toDto(providerService.getActiveById(providerId));
        }
    }

    @Override
    public ProviderVoucherPickupResponseDto pickupProviderVouchers(ProviderVoucherPickupReqDto reqDto) {
        try (var p = new Profiler(getClass(), "pickupProviderVouchers")) {
            validateVoucherPurchaseRequest(reqDto);
            var provider = providerService.getActiveById(reqDto.getProviderId());
            var voucherListing = providerVoucherListingService.getById(reqDto.getVoucherListingId());
            if (!voucherListing.getProviderCode().equals(provider.getCode())) {
                log.error("voucherListingId {} not belong to provider {}", reqDto.getVoucherListingId(), provider.getCode());
                throw new BusinessLogicException(AppErrorCode.REQUEST_DATA_INVALID, "voucherListingId not belong to provider");
            }
            reqDto.setProviderCode(provider.getCode());
            reqDto.setProviderVoucherListingId(voucherListing.getProviderVoucherListingId());
            reqDto.setCurrentQuantity(voucherListing.getQuantity());

            // initiate the purchase request
            var voucherPurchaseRequest = voucherPurchaseRequestHistoryService.initPurchaseRequest(reqDto);

            return VoucherPurchaseMapper.INSTANCE.toPickupResponseDto(voucherPurchaseRequest);
        }
    }

    @Override
    public Page<VoucherPurchasedDto> getPickupProviderVouchersByVoucherImportId(Long providerId, Long voucherListingId, String voucherImportId, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getPickupProviderVouchersByVoucherImportId")) {
            var voucherPurchaseRequestHistory = voucherPurchaseRequestHistoryService.findByVoucherImportId(voucherImportId);
            if (Objects.isNull(voucherPurchaseRequestHistory)) {
                log.error("Voucher purchase request history not found for voucherImportId: {}", voucherImportId);
                throw new BusinessLogicException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, voucherImportId);
            }

            if (!voucherPurchaseRequestHistory.getProviderId().equals(providerId)) {
                log.error("Provider ID mismatch: expected {}, but got {}", voucherPurchaseRequestHistory.getProviderId(), providerId);
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, VoucherPurchaseRequestHistory.Fields.providerCode);
            }

            if (!voucherPurchaseRequestHistory.getVoucherListingId().equals(voucherListingId)) {
                log.error("Voucher Listing ID mismatch: expected {}, but got {}", voucherPurchaseRequestHistory.getVoucherListingId(), voucherListingId);
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, VoucherPurchaseRequestHistory.Fields.voucherListingId);
            }
            if (!voucherPurchaseRequestHistory.getStatus().equals(VoucherPurchaseRequestStatusEnum.COMPLETED)) {
                log.error("Voucher purchase request is not completed: {}", voucherPurchaseRequestHistory.getStatus());
                throw new BusinessLogicException(voucherPurchaseRequestHistory, AppErrorCode.REQUEST_PROCESSING, VoucherPurchaseRequestHistory.Fields.voucherImportId, voucherImportId);
            }

            return voucherPurchasedService.getAllByVoucherImportId(voucherImportId, pageable).map(VoucherPurchaseMapper.INSTANCE::toVoucherPurchasedDto);
        }
    }

    @Override
    public VoucherPurchasedDto rollbackVouchers(Long providerId, Long voucherListingId, String voucherImportId, String voucherCode) {
        try (var p = new Profiler(getClass(), "rollbackVouchers")) {
            if (!StringUtils.hasText(voucherImportId)) {
                log.error("Invalid rollback request: voucherImportId is null or blank");
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "voucherImportId");
            }

            var history = voucherPurchaseRequestHistoryService.findByVoucherImportId(voucherImportId);

            if (Objects.isNull(history)) {
                log.error("Invalid rollback request: voucherImportId={} not found", voucherImportId);
                throw new BusinessLogicException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, voucherImportId);
            }

            if (!history.getProviderId().equals(providerId)) {
                log.error("Invalid rollback request: providerId={} not match with voucherImportId={} providerId={}", providerId, voucherImportId, history.getProviderId());
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, VoucherPurchaseRequestHistory.Fields.providerCode);
            }

            if (!history.getVoucherListingId().equals(voucherListingId)) {
                log.error("Invalid rollback request: voucherListingId={} not match with voucherImportId={} voucherListingId={}", voucherListingId, voucherImportId, history.getVoucherListingId());
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, VoucherPurchaseRequestHistory.Fields.voucherListingId);
            }

            return providerIntegrationServiceFactory.getService(history.getProviderCode()).rollbackVoucher(voucherImportId, voucherCode);
        }
    }

    @Override
    public ProviderVoucherListingDetailsDto getProviderVoucherDetails(Long providerId, Long voucherListingId) {
        try (var p = new Profiler(getClass(), "getProviderVoucherDetails")) {
            var provider = providerService.getActiveById(providerId);
            var voucherListing = providerVoucherListingService.getById(voucherListingId);
            if (!voucherListing.getProviderCode().equals(provider.getCode())) {
                log.error("voucherListingId {} not belong to provider {}", voucherListingId, provider.getCode());
                throw new BusinessLogicException(AppErrorCode.REQUEST_DATA_INVALID, "voucherListingId not belong to provider");
            }

            ProviderVoucherListingDetailsDto voucherListingDetailsDto = providerIntegrationServiceFactory.getService(provider.getCode()).getProviderVoucherListingDetail(voucherListing.getProviderVoucherListingId(), voucherListingId);

            voucherListingDetailsDto.setProviderId(provider.getId());
            voucherListingDetailsDto.setProviderCode(provider.getCode());
            voucherListingDetailsDto.setVoucherListingId(voucherListing.getId());
            voucherListingDetailsDto.setBasePurchasePoint(amountToPoint(voucherListingDetailsDto.getPrice()));
            return voucherListingDetailsDto;
        }
    }

    private void validateVoucherPurchaseRequest(ProviderVoucherPickupReqDto req) {
        if (Objects.isNull(req)) {
            log.error("Invalid purchase request: req is null");
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "req");
        }
        if (Objects.isNull(req.getProviderId())) {
            log.error("Invalid purchase request: providerId is null");
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "providerId");
        }
        if (!StringUtils.hasText(req.getVoucherImportId())) {
            log.error("Invalid purchase request: voucherImportId is null or blank");
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "voucherImportId");
        }
        if (Objects.isNull(req.getVoucherListingId())) {
            log.error("Invalid purchase request: voucherListingId is null");
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "voucherListingId");
        }

        if (Objects.isNull(req.getQuantity()) || req.getQuantity() <= 0) {
            log.error("Invalid purchase request: quantity is null or <= 0");
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "quantity");
        }
    }
}
