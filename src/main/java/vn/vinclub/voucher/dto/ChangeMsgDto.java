package vn.vinclub.voucher.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChangeMsgDto {
    @JsonProperty("id")
    private String msgKey;

    @JsonProperty("value")
    private Long id;

    @JsonProperty("extraValue")
    private String code;
}
